import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHashHistory } from 'vue-router'

export type RouteRecordRawExt = RouteRecordRaw & { hidden?: boolean, children?: RouteRecordRawExt[] }

export const constantRoutes: Array<RouteRecordRawExt> = [
  {
    path: '/redirect',
    // component: Layout,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/Redirect.vue'),
      },
    ],
  },
  {
    path: '/404',
    component: () => import('@/views/404.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/404.vue'),
  },
  {
    path: '/login',
    component: () => import('@/views/login/Login.vue'),
  },
  {
    path: '/',
    redirect: '/home',
    hidden: true,
  },
  {
    path: '/home',
    component: () => import('@/views/Home.vue'),
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
})
export const asyncRoutes: Array<RouteRecordRawExt> = []

export default router
