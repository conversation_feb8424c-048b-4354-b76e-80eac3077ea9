import type { BuildOptions } from 'vite'

export function createBuild(): BuildOptions {
  return {
    chunkSizeWarningLimit: 2000, // 消除打包大小超过500kb警告
    minify: 'esbuild',
    sourcemap: false,
    // 关闭文件计算
    reportCompressedSize: false,
    rollupOptions: {
      output: {
        // 启用代码分割
        manualChunks: {
          // 将大型第三方库分离
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'element-plus': ['element-plus'],
          'echarts': ['echarts'],
          'leaflet': ['leaflet'],
          'editor': ['md-editor-v3', 'marked', 'highlight.js'],
          'utils': ['dayjs', 'axios', '@vueuse/core'],
        },
        // 用于从入口点创建的块的打包输出格式[name]表示文件名,[hash]表示该文件内容hash值
        entryFileNames: 'js/[name].[hash].js',
        // 用于命名代码拆分时创建的共享块的输出命名
        chunkFileNames: 'js/[name].[hash].js',
        // 用于输出静态资源的命名，[ext]表示文件扩展名
        assetFileNames: (assetInfo: any) => {
          const info = assetInfo.name.split('.')
          let extType = info.at(-1)

          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'media'
          }
          else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
            extType = 'img'
          }
          else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'fonts'
          }

          return `${extType}/[name].[hash].[ext]`
        },
      },
    },
  }
}
