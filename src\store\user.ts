import type { LoginData } from '@/api/system/auth'
import type { UserInfo } from '@/api/system/user'
import AuthAPI from '@/api/system/auth'
import { UserAPI } from '@/api/system/user'
import router from '@/router'
import { store } from '@/store/index'
import { TOKEN_KEY } from '@/utils/request'
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: { roles: [], perms: [] } as UserInfo,
  }),
  actions: {
    login(loginData: LoginData) {
      return new Promise<void>((resolve, reject) => {
        AuthAPI.login(loginData)
          .then((data) => {
            const { tokenType, accessToken } = data
            localStorage.setItem(TOKEN_KEY, `${tokenType} ${accessToken}`) // Bearer eyJhbGciOiJIUzI1NiJ9.xxx.xxx
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    getUserInfo() {
      return new Promise<UserInfo>((resolve, reject) => {
        UserAPI.getInfo()
          .then((data) => {
            if (!data) {
              reject(new Error('Verification failed, please Login again.'))
              return
            }

            console.log(data)

            if (!data.roles || data.roles.length <= 0) {
              reject(new Error('getUserInfo: roles must be a non-null array!'))
              return
            }

            this.user = data

            resolve(data)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    logout() {
      return new Promise<void>((resolve, reject) => {
        AuthAPI.logout()
          .then(() => {
            localStorage.setItem(TOKEN_KEY, '')
            location.reload() // 清空路由
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    resetToken() {
      return new Promise<void>((resolve) => {
        localStorage.setItem(TOKEN_KEY, '')
        resetRouter()
        resolve()
      })
    },
  },
})

// 非setup
export function useUserStoreHook() {
  return useUserStore(store)
}

/**
 * 重置路由
 */
export function resetRouter() {
  router.replace({ path: '/login' })
}
