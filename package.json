{"name": "vue-template", "type": "module", "version": "0.0.0", "private": true, "packageManager": "pnpm@10.10.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "prettier": "prettier --write .", "lint": "eslint .", "lint:fix": "pnpm run lint --fix", "test": "vitest --run --coverage --isolate", "style": "stylelint \"src/**/*.(vue|scss|css)\" --fix"}, "dependencies": {"amis": "^6.13.0", "animate.css": "^4.1.1", "axios": "1.10.0", "dayjs": "^1.11.13", "element-plus": "^2.10.3", "mitt": "^3.0.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^4.16.2", "@types/node": "^24.0.12", "@unocss/preset-rem-to-px": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vitest/coverage-v8": "^3.2.4", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.30.1", "eslint-plugin-format": "^1.0.1", "postcss": "^8.5.6", "postcss-px-to-viewport": "^1.1.1", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.2", "stylelint": "^16.21.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-recommended-scss": "^15.0.1", "stylelint-config-recommended-vue": "^1.6.1", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "typescript": "^5.8.3", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "7.0.4", "vite-plugin-restart": "^0.4.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "3.2.4", "vue-tsc": "^3.0.1"}}