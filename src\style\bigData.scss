.transition-area {
  display: flex;
  justify-content: space-between;
  //overflow: hidden;

  > .left {
    width: 403px;
    animation: bounceInLeft 0.5s both;
    flex-shrink: 0;
  }

  > .right {
    width: 403px;
    animation: bounceInRight 0.5s both;
    flex-shrink: 0;
  }

  > .center {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    pointer-events: none;
    .header {
      animation: bounceInDown 0.5s both;
    }
    .footer {
      animation: bounceInUp 0.5s both;
    }
  }

  // 左边添加进场动画
  @keyframes bounceInLeft {
    0%,
    60%,
    75%,
    90%,
    100% {
      animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
      opacity: 0;
      transform: translate3d(-3000px, 0, 0) scaleX(3);
    }

    60% {
      opacity: 1;
      transform: translate3d(25px, 0, 0) scaleX(1);
    }

    75% {
      transform: translate3d(-10px, 0, 0) scaleX(0.98);
    }

    90% {
      transform: translate3d(5px, 0, 0) scaleX(0.995);
    }

    100% {
      transform: translate3d(0, 0, 0);
    }
  }

  // 右边添加进场动画
  @keyframes bounceInRight {
    0%,
    60%,
    75%,
    90%,
    100% {
      animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
      opacity: 0;
      transform: translate3d(3000px, 0, 0) scaleX(3);
    }

    60% {
      opacity: 1;
      transform: translate3d(-25px, 0, 0) scaleX(1);
    }

    75% {
      transform: translate3d(10px, 0, 0) scaleX(0.98);
    }

    90% {
      transform: translate3d(-5px, 0, 0) scaleX(0.995);
    }

    100% {
      transform: translate3d(0, 0, 0);
    }
  }
  // 顶部进场动画（渐变）
  @keyframes bounceInDown {
    0%,
    60%,
    75%,
    90%,
    100% {
      animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  // 中间添加进场动画(从下往上)
  @keyframes bounceInUp {
    0%,
    60%,
    75%,
    90%,
    100% {
      animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
      opacity: 0;
      transform: translate3d(0, 3000px, 0) scaleY(3);
    }

    60% {
      opacity: 1;
      transform: translate3d(0, -25px, 0) scaleY(1);
    }

    75% {
      transform: translate3d(0, 10px, 0) scaleY(0.98);
    }

    90% {
      transform: translate3d(0, -5px, 0) scaleY(0.995);
    }

    100% {
      transform: translate3d(0, 0, 0);
    }
  }
}
