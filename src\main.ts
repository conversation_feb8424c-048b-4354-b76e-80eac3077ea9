import router from '@/router'
import { setupStore } from '@/store'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { createApp } from 'vue'
import App from './App.vue'
import '@/style/index.scss'
import 'normalize.css'
// 原子化css
import 'uno.css'
import './permission'
import 'element-plus/dist/index.css'
// svg-icon
import 'virtual:svg-icons-register'
import 'animate.css'

const app = createApp(App)
setupStore(app)
app.use(ElementPlus, {
  locale: zhCn,
})

app.use(router)
app.mount('#app')
