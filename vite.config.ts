/// <reference types="vitest" />
import path from 'node:path'
import { defineConfig, loadEnv } from 'vite'
import { createBuild } from './vitePlugin/build'
import { createVitePlugins } from './vitePlugin/plugins'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const dirname = import.meta.dirname

  return {
    test: {
      globals: true,
      environment: 'jsdom',
    },
    resolve: {
      alias: {
        '@': path.resolve(dirname, 'src'),
        '#': path.resolve(dirname, 'types'),
      },
    },
    esbuild: {
      pure: ['console.log', 'console.count'], // 删除 console.log
    },
    plugins: createVitePlugins(),
    // 构建配置
    build: createBuild(),

    server: {
      host: '0.0.0.0',
      hmr: true,
      port: 9527,
      open: false,
      proxy: {
        '/api/v1': {
          target: 'https://banquet-web.dayishichangjianguan.com/', // 测试环境
          changeOrigin: true,
        },
      },
    },
  }
})
