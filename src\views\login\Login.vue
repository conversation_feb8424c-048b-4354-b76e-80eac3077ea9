<script setup lang="ts">
import type { LoginData } from '@/api/system/auth'
import type { FormInstance, FormRules } from 'element-plus'
import type { LocationQuery } from 'vue-router'
import AuthAPI from '@/api/system/auth'
import router from '@/router'
import { useUserStore } from '@/store/user'
import { useRoute } from 'vue-router'
import '@/style/login.scss'

const loginData = ref<LoginData>({ username: 'admin', password: '123456' } as LoginData)
const loginRules: FormRules<typeof loginData.value> = {
  username: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
  password: [
    { required: true, trigger: 'blur', message: '请输入密码' },
    { min: 6, message: '密码不能少于6位', trigger: 'blur' },
  ],
  captchaCode: [{ required: true, trigger: 'blur', message: '请输入验证码' }],
}

/**
 * 获取验证码
 */
// 验证码图片Base64字符串
const captchaBase64 = ref()
function getCaptcha() {
  AuthAPI.getCaptcha().then((data) => {
    loginData.value.captchaKey = data.captchaKey
    captchaBase64.value = data.captchaBase64
  })
}

getCaptcha()
/**
 * 登录表单提交
 */
// 按钮 loading 状态
const loading = ref(false)
// 登录表单ref
const loginFormRef = ref<FormInstance>()
const userStore = useUserStore()
function handleLoginSubmit() {
  loginFormRef.value?.validate().then(() => {
    loading.value = true
    userStore
      .login(loginData.value)
      .then(() => {
        const { path, queryParams } = parseRedirect()
        router.push({ path, query: queryParams })
      })
      .catch(() => {
        getCaptcha()
      })
      .finally(() => {
        loading.value = false
      })
  })
}

/**
 * 解析 redirect 字符串 为 path 和  queryParams
 */
const route = useRoute()
function parseRedirect(): { path: string, queryParams: Record<string, string> } {
  const query: LocationQuery = route.query
  const redirect = (query.redirect as string) ?? '/'

  const url = new URL(redirect, window.location.origin)
  const path = url.pathname
  const queryParams: Record<string, string> = {}

  for (const [key, value] of url.searchParams.entries()) {
    queryParams[key] = value
  }

  return { path, queryParams }
}

/**
 * 检查输入大小写
 */
// 是否大写锁定
const isCapslock = ref(false)
function checkCapslock(event: KeyboardEvent) {
  // 防止浏览器密码自动填充时报错
  if (event instanceof KeyboardEvent) {
    isCapslock.value = event.getModifierState('CapsLock')
  }
}
</script>

<template>
  <div class="login-container">
    <!-- 登录表单 -->
    <el-card class="login-card">
      <img src="@/assets/login/title.webp" alt="" class="w-482 h-52 mb-40" />

      <el-form ref="loginFormRef" :model="loginData" :rules="loginRules" class="login-form bg-white rounded-12 p-52">
        <!-- 用户名 -->
        <el-form-item prop="username">
          <div class="input-wrapper">
            <svg-icon name="user" class="mx-6" />
            <el-input v-model="loginData.username" placeholder="用户名" name="username" size="large" class="h-48" />
          </div>
        </el-form-item>

        <!-- 密码 -->
        <el-tooltip :visible="isCapslock" content="大写锁定已打开" placement="right">
          <el-form-item prop="password">
            <div class="input-wrapper">
              <svg-icon name="lock" class="mx-6" />
              <el-input
                v-model="loginData.password"
                placeholder="密码"
                type="password"
                name="password"
                size="large"
                class="h-48 pr-10"
                show-password
                @keyup="checkCapslock"
                @keyup.enter="handleLoginSubmit"
              />
            </div>
          </el-form-item>
        </el-tooltip>

        <!-- 验证码 -->
        <el-form-item prop="captchaCode">
          <div class="input-wrapper">
            <svg-icon name="captcha" class="mx-6" />
            <el-input v-model="loginData.captchaCode" auto-complete="off" size="large" class="flex-1" placeholder="验证码" @keyup.enter="handleLoginSubmit" />

            <el-image :src="captchaBase64" class="captcha-image" @click="getCaptcha" />
          </div>
        </el-form-item>

        <!-- 登录按钮 -->
        <el-button :loading="loading" type="primary" size="large" class="w-full mt-30" @click.prevent="handleLoginSubmit">登录</el-button>
      </el-form>
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
