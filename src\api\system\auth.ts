import request from '@/utils/request'

const AUTH_BASE_URL = '/api/v1/auth'

const AuthAPI = {
  /** 登录 接口 */
  login(data: LoginData) {
    const formData = new FormData()
    formData.append('username', data.username)
    formData.append('password', data.password)
    formData.append('captchaKey', data.captchaKey)
    formData.append('captchaCode', data.captchaCode)
    formData.append('type', 0)
    return request<any, LoginResult>({
      url: `${AUTH_BASE_URL}/login`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  /** 注销 接口 */
  logout() {
    return request({
      url: `${AUTH_BASE_URL}/logout`,
      method: 'delete',
    })
  },

  /** 获取验证码 接口 */
  getCaptcha() {
    return request<any, CaptchaResult>({
      url: `${AUTH_BASE_URL}/captcha`,
      method: 'get',
    })
  },
}

export default AuthAPI

/** 登录请求参数 */
export interface LoginData {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 验证码缓存key */
  captchaKey: string
  /** 验证码 */
  captchaCode: string
  // 0 管理端 1移动端 2大数据端
  type: 0 | 1 | 2
}

/** 登录响应 */
export interface LoginResult {
  /** 访问token */
  accessToken?: string
  /** 过期时间(单位：毫秒) */
  expires?: number
  /** 刷新token */
  refreshToken?: string
  /** token 类型 */
  tokenType?: string
}

/** 验证码响应 */
export interface CaptchaResult {
  /** 验证码缓存key */
  captchaKey: string
  /** 验证码图片Base64字符串 */
  captchaBase64: string
}
