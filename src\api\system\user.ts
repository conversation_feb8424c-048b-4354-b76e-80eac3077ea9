import request from '@/utils/request'

const BASE_URL = '/api/v1/users'

export const UserAPI = {
  /**
   * 获取当前登录用户信息
   *
   * @returns 登录用户昵称、头像信息，包括角色和权限
   */
  getInfo() {
    return request<any, UserInfo>({
      url: `${BASE_URL}/me`,
      method: 'get',
    })
  },

  // 部门人员树
  getDeptUserTree() {
    return request<any, any>({
      url: `/api/v1/dept/optionsDeptUser`,
      method: 'get',
    })
  },
}

/** 登录用户信息 */
export interface UserInfo {
  /** 用户ID */
  userId?: number

  /** 用户名 */
  username?: string

  /** 昵称 */
  nickname?: string

  /** 头像URL */
  avatar?: string

  /** 角色 */
  roles: string[]

  /** 权限 */
  perms: string[]
}
